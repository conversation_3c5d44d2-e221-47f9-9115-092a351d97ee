<template>
  <div class="home-container">
    <header class="header">
      <h1>欢迎来到安心签</h1>
            <div>
        <router-link to="/profile" class="profile-button">个人中心</router-link>
        <button @click="logout" class="logout-button">退出登录</button>
      </div>
    </header>
    <main class="main-content">
      <p>在这里，您可以开始您的每日签到。</p>
      <button @click="checkin" class="checkin-button">每日签到</button>
      <p v-if="message" class="message">{{ message }}</p>
      <div class="checkin-history">
        <h2>签到记录</h2>
        <ul>
          <li v-for="item in checkins" :key="item.id">
            {{ new Date(item.checkinDate).toLocaleDateString() }}
          </li>
        </ul>
      </div>
    </main>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import axios from 'axios';

const router = useRouter();
const message = ref('');
const checkins = ref([]);

const api = axios.create({
  baseURL: 'http://localhost:3000/api',
  headers: {
    'x-access-token': localStorage.getItem('token'),
  },
});

const fetchCheckins = async () => {
  try {
    const response = await api.get('/checkin');
    checkins.value = response.data;
  } catch (error) {
    console.error('获取签到记录失败', error);
    if (error.response && error.response.status === 404) {
      checkins.value = [];
    } else {
       message.value = '无法加载签到记录。';
    }
  }
};

const checkin = async () => {
  try {
    await api.post('/checkin');
    message.value = '签到成功！';
    fetchCheckins();
  } catch (error) {
    console.error('签到失败', error);
    if (error.response && error.response.data.message === 'already checked in today') {
      message.value = '今日已签到，请勿重复操作。';
    } else {
      message.value = '签到失败，请稍后再试。';
    }
  }
};

const logout = () => {
  localStorage.removeItem('token');
  router.push('/login');
};

onMounted(() => {
  fetchCheckins();
});
</script>

<style scoped>
.home-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 2rem;
  font-family: Arial, sans-serif;
}

.header {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.profile-button {
  margin-right: 1rem;
  padding: 0.5rem 1rem;
  background-color: #007bff;
  color: white;
  text-decoration: none;
  border-radius: 4px;
}

.logout-button {
  padding: 0.5rem 1rem;
  border: none;
  background-color: #f44336;
  color: white;
  border-radius: 4px;
  cursor: pointer;
}

.main-content {
  text-align: center;
}

.checkin-button {
  background-color: #4CAF50;
  color: white;
  padding: 10px 20px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 16px;
  margin-bottom: 1rem;
}

.message {
  color: #333;
  min-height: 24px;
}

.checkin-history {
  margin-top: 2rem;
  width: 300px;
}

.checkin-history h2 {
  border-bottom: 2px solid #eee;
  padding-bottom: 0.5rem;
}

.checkin-history ul {
  list-style: none;
  padding: 0;
}

.checkin-history li {
  background-color: #f9f9f9;
  padding: 0.8rem;
  border-radius: 4px;
  margin-top: 0.5rem;
}
</style>