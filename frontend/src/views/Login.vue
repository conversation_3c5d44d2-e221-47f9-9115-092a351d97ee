<template>
  <div class="login-container">
    <h2>登录</h2>
    <form @submit.prevent="handleLogin">
      <div class="form-group">
        <label for="account">账号 (手机号/邮箱)</label>
        <input type="text" id="account" v-model="account" required>
      </div>
      <div class="form-group">
        <label for="password">密码</label>
        <input type="password" id="password" v-model="password" required>
      </div>
      <button type="submit">登录</button>
      <p v-if="errorMessage" class="error-message">{{ errorMessage }}</p>
    </form>
    <p>还没有账号? <router-link to="/register">立即注册</router-link></p>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import axios from 'axios';
import { useRouter } from 'vue-router';

const account = ref('');
const password = ref('');
const errorMessage = ref('');
const router = useRouter();

/**
 * @description 处理登录逻辑
 */
const handleLogin = async () => {
  try {
    const response = await axios.post('/api/auth/login', {
      account: account.value,
      password: password.value
    });
    // 假设登录成功后，后端返回token
    localStorage.setItem('token', response.data.token);
    // 登录成功后可以跳转到首页或其他页面
    router.push('/'); 
  } catch (error) {
    errorMessage.value = error.response?.data?.message || '登录失败，请稍后再试';
    console.error('登录失败:', error);
  }
};
</script>

<style scoped>
.login-container {
  max-width: 400px;
  margin: 50px auto;
  padding: 20px;
  border: 1px solid #ccc;
  border-radius: 5px;
}
.form-group {
  margin-bottom: 15px;
}
.form-group label {
  display: block;
  margin-bottom: 5px;
}
.form-group input {
  width: 100%;
  padding: 8px;
  box-sizing: border-box;
}
button {
  width: 100%;
  padding: 10px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
}
button:hover {
  background-color: #0056b3;
}
.error-message {
  color: red;
  margin-top: 10px;
}
</style>