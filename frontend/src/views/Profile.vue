<template>
  <div class="profile-container">
    <h2>个人中心</h2>
    
    <div class="emergency-contacts-section">
      <h3>紧急联系人管理</h3>
      <div class="contact-form">
        <input v-model="newContact.name" placeholder="姓名" />
        <input v-model="newContact.phone" placeholder="手机号" />
        <button @click="addContact">添加联系人</button>
      </div>
      
      <ul class="contact-list">
        <li v-for="contact in contacts" :key="contact.id">
          <span>{{ contact.name }} - {{ contact.phone }}</span>
          <button @click="deleteContact(contact.id)">删除</button>
        </li>
      </ul>
    </div>

  </div>
</template>

<script>
import axios from 'axios';

export default {
  data() {
    return {
      newContact: {
        name: '',
        phone: ''
      },
      contacts: []
    };
  },
  created() {
    this.fetchContacts();
  },
  methods: {
    /**
     * 获取当前用户的所有紧急联系人
     */
    async fetchContacts() {
      try {
        const token = localStorage.getItem('token');
        const response = await axios.get('http://localhost:3000/api/emergency-contacts', {
          headers: { 'x-access-token': token }
        });
        this.contacts = response.data;
      } catch (error) {
        console.error("获取紧急联系人失败:", error);
      }
    },
    /**
     * 添加一个新的紧急联系人
     */
    async addContact() {
      if (!this.newContact.name || !this.newContact.phone) {
        alert('姓名和手机号不能为空');
        return;
      }
      try {
        const token = localStorage.getItem('token');
        await axios.post('http://localhost:3000/api/emergency-contacts', this.newContact, {
          headers: { 'x-access-token': token }
        });
        this.newContact.name = '';
        this.newContact.phone = '';
        this.fetchContacts(); // 重新加载列表
      } catch (error) {
        console.error("添加紧急联系人失败:", error);
      }
    },
    /**
     * 根据ID删除一个紧急联系人
     */
    async deleteContact(id) {
      try {
        const token = localStorage.getItem('token');
        await axios.delete(`http://localhost:3000/api/emergency-contacts/${id}`, {
          headers: { 'x-access-token': token }
        });
        this.fetchContacts(); // 重新加载列表
      } catch (error) {
        console.error("删除紧急联系人失败:", error);
      }
    }
  }
};
</script>

<style scoped>
.profile-container {
  max-width: 600px;
  margin: 2rem auto;
  padding: 2rem;
  background-color: #f9f9f9;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

h3 {
  margin-bottom: 1.5rem;
}

.contact-form {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
}

.contact-form input {
  flex-grow: 1;
  padding: 0.5rem;
  border: 1px solid #ccc;
  border-radius: 4px;
}

.contact-form button {
  padding: 0.5rem 1rem;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.contact-list li {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  border-bottom: 1px solid #eee;
}

.contact-list button {
  padding: 0.3rem 0.6rem;
  background-color: #dc3545;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}
</style>