<template>
  <div class="register-container">
    <h2>注册</h2>
    <form @submit.prevent="handleRegister">
      <div class="form-group">
        <label for="nickname">昵称</label>
        <input type="text" id="nickname" v-model="nickname" required>
      </div>
      <div class="form-group">
        <label for="phone">手机号</label>
        <input type="tel" id="phone" v-model="phone">
      </div>
      <div class="form-group">
        <label for="email">邮箱</label>
        <input type="email" id="email" v-model="email">
      </div>
      <div class="form-group">
        <label for="password">密码</label>
        <input type="password" id="password" v-model="password" required>
      </div>
      <button type="submit">注册</button>
      <p v-if="successMessage" class="success-message">{{ successMessage }}</p>
      <p v-if="errorMessage" class="error-message">{{ errorMessage }}</p>
    </form>
    <p>已有账号? <router-link to="/login">立即登录</router-link></p>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import axios from 'axios';
import { useRouter } from 'vue-router';

const nickname = ref('');
const phone = ref('');
const email = ref('');
const password = ref('');
const successMessage = ref('');
const errorMessage = ref('');
const router = useRouter();

/**
 * @description 处理注册逻辑
 */
const handleRegister = async () => {
  if (!phone.value && !email.value) {
    errorMessage.value = '手机号和邮箱至少需要填写一个';
    return;
  }
  
  try {
    await axios.post('/api/auth/signup', {
      nickname: nickname.value,
      phone: phone.value,
      email: email.value,
      password: password.value
    });
    successMessage.value = '注册成功！正在跳转到登录页面...';
    errorMessage.value = '';
    setTimeout(() => {
      router.push('/login');
    }, 2000);
  } catch (error) {
    errorMessage.value = error.response?.data?.message || '注册失败，请稍后再试';
    successMessage.value = '';
    console.error('注册失败:', error);
  }
};
</script>

<style scoped>
.register-container {
  max-width: 400px;
  margin: 50px auto;
  padding: 20px;
  border: 1px solid #ccc;
  border-radius: 5px;
}
.form-group {
  margin-bottom: 15px;
}
.form-group label {
  display: block;
  margin-bottom: 5px;
}
.form-group input {
  width: 100%;
  padding: 8px;
  box-sizing: border-box;
}
button {
  width: 100%;
  padding: 10px;
  background-color: #28a745;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
}
button:hover {
  background-color: #218838;
}
.error-message {
  color: red;
  margin-top: 10px;
}
.success-message {
  color: green;
  margin-top: 10px;
}
</style>