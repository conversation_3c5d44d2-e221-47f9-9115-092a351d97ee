# 产品需求文档 (PRD): “安心签” - 每日签到与安全确认应用

## 1. 项目概述

### 1.1 项目背景

在快节奏的现代生活中，人们越来越关注个人习惯的养成和亲友的安全。市场上的签到应用功能丰富，但大多侧重于习惯养成和任务管理。我们将开发一款名为“安心签”的Web应用，它不仅具备传统签到应用的核心功能，还创新性地加入了“安全确认”机制。当用户在预设时间内未完成签到，系统将自动向其指定的紧急联系人发送提醒，为用户的安全提供多一重保障，尤其适合独居人士、老人或需要特别关注的人群。

### 1.2 目标用户

- 希望养成良好生活习惯的用户（如坚持运动、学习、健康饮食等）。
- 关心亲友安全的家庭成员。
- 独居人士、老年人、或需要定期向家人报平安的用户。
- 需要进行成员管理的组织或团体（如企业、社团、兴趣小组等）。

### 1.3 核心价值

- **习惯养成**: 通过签到、打卡、排行榜等功能，激励用户养成并坚持良好习惯。
- **安全守护**: 创新的“安全确认”功能，在用户可能遇到意外时，及时通知其亲人，传递关爱与安心。
- **社群互动**: 提供社群功能，用户可以创建或加入圈子，与志同道合的人一起打卡、互相监督、共同进步。

## 2. 产品功能详述

### 2.1 用户模块

#### 2.1.1 注册与登录
- **注册**: 用户可以通过手机号/邮箱进行注册，需要设置密码。
- **登录**: 支持手机号/邮箱+密码登录。

#### 2.1.2 个人中心
- **个人信息**: 用户可以编辑自己的昵称、头像、个人简介。
- **我的签到**: 展示用户参与的所有签到活动列表及历史记录。
- **紧急联系人设置**: 用户可以添加/修改/删除紧急联系人（姓名、手机号）。这是“安全确认”功能的核心。
- **安全状态设置**: 用户可以设置每日需确认安全的时间段。例如，每天20:00 - 22:00之间需要签到一次来确认安全。

### 2.2 签到模块

#### 2.2.1 签到活动
- **创建签到**: 用户可以创建公开或私密的签到活动。
  - **活动名称/描述**: 定义签到主题。
  - **签到规则**: 设置签到频率（每日/每周/指定日期）、每日可签到时间段。
  - **参与方式**: 公开（任何人可加入）或私密（凭邀请码或管理员审核加入）。
- **发现签到**: 用户可以浏览和搜索平台上的公开签到活动。
- **签到操作**: 在指定时间内，用户可以点击“签到”按钮完成打卡，可附带文字、图片记录。

#### 2.2.2 排行榜
- **签到排行**: 根据签到天数、连续签到次数等维度进行排名，激励用户。
- **排行榜类型**: 分为总榜、周榜、日榜。

### 2.3 安全确认模块

#### 2.3.1 安全确认机制
- 用户在个人中心开启“安全确认”功能，并设置好紧急联系人和安全确认时间段。
- 用户需要在指定时间段内，在“安全签到”这个特殊的签到项中完成一次签到。

#### 2.3.2 提醒与警报
- **签到提醒**: 在安全确认时间段开始时，系统会向用户发送签到提醒。
- **超时警报**: 如果用户在指定时间段结束时仍未完成安全签到，系统将自动向其预设的紧急联系人发送短信提醒。短信内容示例：“【安心签】提醒：您关心的人[用户昵称]在过去24小时内未进行安全确认，请您及时联系确认其安全。”

### 2.4 社群（圈子）模块

- **创建/加入圈子**: 用户可以创建自己的圈子，或加入感兴趣的圈子。
- **圈内签到**: 圈主可以发起圈内专属的签到活动。
- **圈内动态**: 成员的签到动态会在圈子内展示，方便互相监督和鼓励。

## 3. 非功能性需求

- **性能**: 应用响应迅速，页面加载时间不超过3秒。
- **安全性**: 用户密码需加密存储，通信采用HTTPS协议，保护用户隐私数据。
- **可用性**: 界面简洁美观，操作流程直观易懂，无需过多学习成本。
- **兼容性**: 兼容主流浏览器（Chrome, Firefox, Safari, Edge）的最新版本。

## 4. 技术栈选型 (建议)

- **前端**: Vue.js / React (选择一个现代前端框架)
- **后端**: Node.js (Express / Koa) / Python (Django / Flask)
- **数据库**: MySQL / PostgreSQL
- **缓存**: Redis (用于存储排行榜等高频访问数据)
- **短信服务**: 阿里云/腾讯云等第三方短信服务API

## 5. 里程碑 (Milestone)

- **第一阶段 (MVP - 最小可行产品)**: 
  - 完成用户注册登录功能。
  - 实现核心的签到打卡功能（创建、加入、执行）。
  - 实现核心的“安全确认”与短信报警功能。
- **第二阶段**: 
  - 开发排行榜功能。
  - 增加社群（圈子）功能。
  - 优化用户体验和界面设计。
- **第三阶段**: 
  - 完善数据统计与分析功能。
  - 根据用户反馈进行迭代优化。