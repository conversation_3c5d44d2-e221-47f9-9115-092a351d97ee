-- 为“安心签”应用创建数据库和表

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS `anxinqian` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE `anxinqian`;

-- 用户表
CREATE TABLE IF NOT EXISTS `users` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `nickname` VARCHAR(50) NOT NULL,
  `password` VARCHAR(255) NOT NULL COMMENT '存储哈希后的密码',
  `phone` VARCHAR(20) UNIQUE,
  `email` VARCHAR(100) UNIQUE,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;