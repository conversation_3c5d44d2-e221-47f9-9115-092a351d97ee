const express = require('express');
const router = express.Router();
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const db = require('../db');

const jwtSecret = 'your_jwt_secret'; // 强烈建议使用环境变量来存储密钥

/**
 * @description 用户注册
 * @param {string} nickname - 昵称
 * @param {string} password - 密码
 * @param {string} phone - 手机号 (可选)
 * @param {string} email - 邮箱 (可选)
 */
router.post('/register', async (req, res) => {
  const { nickname, password, phone, email } = req.body;

  if (!nickname || !password) {
    return res.status(400).json({ message: '昵称和密码不能为空' });
  }

  try {
    // 检查用户是否已存在
    const [existingUsers] = await db.execute('SELECT * FROM users WHERE phone = ? OR email = ?', [phone, email]);
    if (existingUsers.length > 0) {
        return res.status(409).json({ message: '手机号或邮箱已被注册' });
    }

    // 哈希密码
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(password, salt);

    // 插入新用户
    const [result] = await db.execute(
      'INSERT INTO users (nickname, password, phone, email) VALUES (?, ?, ?, ?)',
      [nickname, hashedPassword, phone, email]
    );

    res.status(201).json({ message: '用户注册成功', userId: result.insertId });
  } catch (error) {
    res.status(500).json({ message: '服务器错误', error: error.message });
  }
});

/**
 * @description 用户登录
 * @param {string} account - 手机号或邮箱
 * @param {string} password - 密码
 */
router.post('/login', async (req, res) => {
  const { account, password } = req.body;

  if (!account || !password) {
    return res.status(400).json({ message: '账户和密码不能为空' });
  }

  try {
    // 查找用户
    const [users] = await db.execute('SELECT * FROM users WHERE phone = ? OR email = ?', [account, account]);
    if (users.length === 0) {
      return res.status(401).json({ message: '无效的凭证' });
    }

    const user = users[0];

    // 比较密码
    const isMatch = await bcrypt.compare(password, user.password);
    if (!isMatch) {
      return res.status(401).json({ message: '无效的凭证' });
    }

    // 创建JWT
    const payload = { user: { id: user.id } };
    const token = jwt.sign(payload, jwtSecret, { expiresIn: '1h' });

    res.json({ token });
  } catch (error) {
    res.status(500).json({ message: '服务器错误', error: error.message });
  }
});

module.exports = router;