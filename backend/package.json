{"name": "backend", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "nodemon index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"bcryptjs": "^3.0.2", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "mysql2": "^3.14.2", "node-cron": "^3.0.3"}, "devDependencies": {"nodemon": "^3.1.10"}}