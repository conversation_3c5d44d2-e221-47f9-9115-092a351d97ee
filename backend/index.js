const express = require('express');
const app = express();
const port = 3000;
const db = require('./db'); // 引入数据库连接

// 中间件
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// 简单的路由
app.get('/', (req, res) => {
  res.json({ message: '欢迎来到安心签应用后端.' });
});

// 引入路由
const authRoutes = require('./routes/auth.routes');

// 使用路由
app.use('/api/auth', authRoutes);
require("./app/routes/checkin.routes.js")(app);
require("./app/routes/emergency_contact.routes.js")(app);
require("./app/routes/auth.routes.js")(app);

// 引入并启动安全检查服务
const safetyCheckService = require("./app/services/safety_check.service.js");
safetyCheckService.start();

// 测试数据库连接
app.get('/db-test', async (req, res) => {
  try {
    const [rows, fields] = await db.execute('SELECT 1 + 1 AS solution');
    res.json({ message: '数据库连接成功', result: rows[0].solution });
  } catch (error) {
    res.status(500).json({ message: '数据库连接失败', error: error.message });
  }
});


app.listen(port, () => {
  console.log(`服务器正在端口 ${port} 上运行.`);
});