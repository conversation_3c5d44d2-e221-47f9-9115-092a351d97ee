const mysql = require('mysql2');
const dbConfig = require('./config/db.config.js');

// 创建连接池
const pool = mysql.createPool({
  host: dbConfig.HOST,
  user: dbConfig.USER,
  password: dbConfig.PASSWORD,
  database: dbConfig.DB,
  waitForConnections: true,
  connectionLimit: dbConfig.pool.max,
  queueLimit: 0
});

// 将连接池Promise化，以便使用async/await
const promisePool = pool.promise();

// 导出promise化的连接池
module.exports = promisePool;